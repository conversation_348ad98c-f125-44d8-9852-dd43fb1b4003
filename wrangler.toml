# Cloudflare Pages 配置
name = "shipsaas-office"
compatibility_date = "2024-12-01"
compatibility_flags = ["nodejs_compat"]

# Pages 构建输出目录
pages_build_output_dir = ".open-next"

# 生产环境配置
[env.production]
name = "shipsaas-office-prod"
vars = { NODE_ENV = "production" }

# 预览环境配置
[env.preview]
name = "shipsaas-office-preview"
vars = { NODE_ENV = "development" }

# KV 存储配置（如果需要）
# [[kv_namespaces]]
# binding = "MY_KV_NAMESPACE"
# id = "your-kv-namespace-id"

# D1 数据库配置（如果使用 Cloudflare D1）
# [[d1_databases]]
# binding = "DB"
# database_name = "shipsaas-db"
# database_id = "your-database-id"

# 环境变量（敏感信息）
# 在 Cloudflare Pages 控制台中设置这些变量
# DATABASE_URL
# NEXTAUTH_SECRET
# NEXTAUTH_URL
# GOOGLE_CLIENT_ID
# GOOGLE_CLIENT_SECRET
# STRIPE_PRIVATE_KEY
# STRIPE_WEBHOOK_SECRET
# GITHUB_TOKEN
