# Vercel 部署错误修复

## 问题描述

Vercel 部署时遇到以下错误：
```
npm error Cannot read properties of null (reading 'matches')
Error: Command "npm install" exited with 1
```

## 问题原因

1. **包管理器冲突**: 项目使用 pnpm，但 Vercel 尝试使用 npm 安装依赖
2. **依赖版本冲突**: 某些 @types 包放在了错误的依赖分类中
3. **构建配置不兼容**: Next.js 配置中的 `output: 'standalone'` 与 Vercel 冲突

## 修复方案

### 1. 移除 pnpm 配置
- 从 `package.json` 中移除 `packageManager` 字段
- 移除 `engines.pnpm` 配置
- 让 Vercel 使用默认的 npm

### 2. 修复依赖分类
- 将 `@types/google-one-tap` 和 `@types/uuid` 从 `dependencies` 移动到 `devDependencies`

### 3. 创建 package-lock.json
- 手动创建基本的 `package-lock.json` 文件
- 确保 Vercel 使用 npm 而不是 pnpm

### 4. 优化 Next.js 配置
- 修改 `next.config.mjs` 中的 `output` 配置
- 仅在非 Vercel 环境中使用 `standalone` 模式

### 5. 更新 Vercel 配置
- 简化 `vercel.json` 配置
- 使用标准的 `npm run build` 命令
- 添加环境变量跳过验证

### 6. 优化 .npmrc 配置
- 添加 `strict-peer-deps=false`
- 禁用 `fund` 和 `audit` 以加速安装

## 修复后的文件

### package.json
```json
{
  "engines": {
    "node": ">=18.17.0"
  }
}
```

### vercel.json
```json
{
  "buildCommand": "npm run build",
  "framework": "nextjs",
  "regions": ["hkg1"],
  "functions": {
    "app/api/**/*.js": {
      "maxDuration": 30
    }
  },
  "env": {
    "SKIP_ENV_VALIDATION": "1"
  }
}
```

### next.config.mjs
```javascript
// 输出配置 - 仅在非 Vercel 环境中使用 standalone
output: process.env.VERCEL ? undefined : 'standalone',
```

### .npmrc
```
legacy-peer-deps=true
auto-install-peers=true
strict-peer-deps=false
fund=false
audit=false
```

## 验证步骤

1. 确认 `package-lock.json` 文件存在
2. 确认 `package.json` 中没有 `packageManager` 字段
3. 确认所有 `@types/*` 包都在 `devDependencies` 中
4. 重新部署到 Vercel

## 注意事项

- 本地开发仍然可以使用 pnpm
- Cloudflare Pages 部署不受影响
- 如果需要恢复 pnpm，可以重新添加 `packageManager` 字段并删除 `package-lock.json`

## 相关文件

- `package.json` - 依赖配置
- `package-lock.json` - npm 锁定文件
- `vercel.json` - Vercel 部署配置
- `next.config.mjs` - Next.js 配置
- `.npmrc` - npm 配置
- `.nvmrc` - Node.js 版本配置
