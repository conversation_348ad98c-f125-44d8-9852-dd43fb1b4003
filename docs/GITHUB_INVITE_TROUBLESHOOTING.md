# GitHub 邀请功能故障排除指南

## 问题分析

根据错误信息 `GitHub username not found or repository not accessible`，这是一个400错误，通常由以下原因引起：

## 常见问题及解决方案

### 1. GitHub Token 配置问题

#### 问题症状
- 400 Bad Request 错误
- "GitHub username not found or repository not accessible"

#### 解决方案
1. **检查 Token 是否配置**
   ```bash
   # 确保 .env 文件中有 GitHub Token
   GITHUB_API_TOKEN=ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
   ```

2. **验证 Token 权限**
   访问权限检查页面：http://localhost:3001/en/demo/github-permissions
   
   Token 需要以下权限：
   - ✅ `repo` - 完整的仓库访问权限
   - ✅ `admin:org` - 组织管理权限（如果仓库属于组织）

3. **创建正确的 GitHub Token**
   - 访问：https://github.com/settings/tokens
   - 点击 "Generate new token (classic)"
   - 选择权限：
     - [x] repo (Full control of private repositories)
     - [x] admin:org (Full control of orgs and teams)
   - 复制生成的 token 到 .env 文件

### 2. 仓库访问权限问题

#### 问题症状
- 403 Forbidden 错误
- "Insufficient permissions"

#### 解决方案
1. **确认 Token 所有者权限**
   - Token 所有者必须是 `ShipSaaSCo/shipsaas-starter` 仓库的管理员或所有者
   - 如果是组织仓库，需要组织管理员权限

2. **检查仓库存在性**
   - 确认仓库 `ShipSaaSCo/shipsaas-starter` 存在
   - 确认仓库是私有仓库
   - 确认仓库路径正确

### 3. GitHub 用户名问题

#### 问题症状
- 404 Not Found 错误
- "GitHub username not found"

#### 解决方案
1. **验证用户名格式**
   - GitHub 用户名只能包含字母、数字和连字符
   - 不能以连字符开头或结尾
   - 最长 39 个字符

2. **确认用户存在**
   - 访问 `https://github.com/用户名` 确认用户存在
   - 确认用户名拼写正确

### 4. API 调用问题

#### 问题症状
- 422 Unprocessable Entity 错误
- "Validation failed"

#### 解决方案
1. **检查用户是否已是协作者**
   - 用户可能已经是仓库协作者
   - 检查仓库的 Settings > Collaborators

2. **检查邀请是否已存在**
   - 可能已经发送过邀请但用户未接受
   - 检查仓库的 Settings > Collaborators > Pending invitations

## 调试步骤

### 步骤 1: 检查 Token 配置
```bash
# 1. 确认 .env 文件存在且包含 GitHub Token
cat .env | grep GITHUB_API_TOKEN

# 2. 重启开发服务器
npm run dev
```

### 步骤 2: 使用权限检查工具
1. 访问：http://localhost:3001/en/demo/github-permissions
2. 点击 "Check Permissions" 按钮
3. 查看检查结果：
   - ✅ Token 配置状态
   - ✅ 用户认证状态
   - ✅ Token 权限范围
   - ✅ 仓库访问权限
   - ✅ 速率限制状态

### 步骤 3: 测试 API 调用
```bash
# 使用 curl 测试 GitHub API
curl -X GET \
  -H "Accept: application/vnd.github+json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "X-GitHub-Api-Version: 2022-11-28" \
  https://api.github.com/repos/ShipSaaSCo/shipsaas-starter
```

### 步骤 4: 检查服务器日志
查看开发服务器控制台输出，寻找详细的错误信息：
```
GitHub API response status: 404
GitHub API error details: {
  status: 404,
  error: { message: "Not Found" }
}
```

## 常见错误代码及含义

| 状态码 | 含义 | 可能原因 | 解决方案 |
|--------|------|----------|----------|
| 400 | Bad Request | 请求参数错误 | 检查用户名格式 |
| 401 | Unauthorized | Token 无效 | 检查 Token 配置 |
| 403 | Forbidden | 权限不足 | 检查 Token 权限 |
| 404 | Not Found | 用户或仓库不存在 | 检查用户名和仓库路径 |
| 422 | Unprocessable Entity | 验证失败 | 检查是否已是协作者 |

## 成功的 API 响应

### 新邀请 (201 Created)
```json
{
  "id": 1,
  "login": "username",
  "permissions": "pull",
  "created_at": "2024-01-01T00:00:00Z"
}
```

### 现有协作者 (204 No Content)
- 无响应体
- 表示用户已经是协作者

## 验证邀请成功

1. **检查 GitHub 仓库**
   - 访问：https://github.com/ShipSaaSCo/shipsaas-starter/settings/access
   - 查看 "Pending invitations" 部分

2. **用户端验证**
   - 用户检查 GitHub 通知
   - 用户访问：https://github.com/notifications
   - 查找仓库邀请通知

## 生产环境注意事项

1. **Token 安全**
   - 不要在代码中硬编码 Token
   - 使用环境变量存储 Token
   - 定期轮换 Token

2. **权限最小化**
   - 只授予必要的权限
   - 考虑使用 GitHub App 替代 Personal Access Token

3. **错误监控**
   - 监控 API 调用失败率
   - 记录详细的错误日志
   - 设置告警机制

## 联系支持

如果按照以上步骤仍无法解决问题，请提供以下信息：

1. 权限检查页面的完整输出
2. 服务器控制台的错误日志
3. 使用的 GitHub 用户名
4. Token 的权限范围截图（不要包含 Token 本身）

## 相关链接

- [GitHub REST API 文档](https://docs.github.com/en/rest/collaborators/collaborators)
- [GitHub Personal Access Token 创建指南](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token)
- [GitHub API 权限说明](https://docs.github.com/en/rest/overview/permissions-required-for-github-apps)
