# ✅ Cloudflare Pages 部署检查清单

## 🔧 构建配置检查

### ✅ 已完成的配置
- [x] 安装 `@opennextjs/cloudflare@1.4.0`
- [x] 创建 `wrangler.toml` 配置文件
- [x] 配置 `pages_build_output_dir = ".open-next"`
- [x] 创建专用构建脚本 `scripts/build-cloudflare.sh`
- [x] 更新 `package.json` 构建命令
- [x] 添加 `public/_headers` 安全配置
- [x] 添加 `public/_redirects` 重定向规则
- [x] 移除冲突的 edge runtime 配置

### 🚀 Cloudflare Pages 设置

#### 1. 连接 GitHub 仓库
```
1. 登录 Cloudflare Dashboard
2. 进入 Pages > Create a project
3. 选择 "Connect to Git"
4. 选择您的 GitHub 仓库: wenhaofree/shipsaas-office
```

#### 2. 构建设置
```
Framework preset: None (自定义)
Build command: pnpm run build:cloudflare
Build output directory: .open-next
Root directory: / (留空)
Environment variables: NODE_ENV=production
```

#### 3. 环境变量设置
在 Cloudflare Pages 项目设置中添加：

**必需的环境变量:**
```
DATABASE_URL=your_database_url
NEXTAUTH_SECRET=your_nextauth_secret_32_chars_min
NEXTAUTH_URL=https://your-domain.pages.dev
```

**OAuth 提供商:**
```
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
```

**支付集成:**
```
STRIPE_PRIVATE_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

**GitHub 集成:**
```
GITHUB_TOKEN=ghp_...
```

## 🔍 部署前检查

### 本地测试
```bash
# 1. 清理之前的构建
rm -rf .next .open-next

# 2. 本地构建测试
pnpm run build:cloudflare

# 3. 检查输出文件
ls -la .open-next/
# 应该看到: worker.js, assets/, cache/, middleware/, server-functions/

# 4. 本地预览（可选）
pnpm run preview:cloudflare
```

### 构建输出验证
确保以下文件存在：
- [x] `.open-next/worker.js` - 主 Worker 文件
- [x] `.open-next/assets/` - 静态资源
- [x] `.open-next/cache/` - 缓存配置
- [x] `.open-next/middleware/` - 中间件
- [x] `.open-next/server-functions/` - 服务器函数

## 🚨 常见问题解决

### 问题 1: "routes were not configured to run with the Edge Runtime"
**原因**: 使用了 `@cloudflare/next-on-pages` 而不是 `@opennextjs/cloudflare`
**解决**: 确保 Cloudflare Pages 使用正确的构建命令

### 问题 2: "wrangler.toml file was found but it does not appear to be valid"
**原因**: 缺少 `pages_build_output_dir` 配置
**解决**: 已在 `wrangler.toml` 中添加此配置

### 问题 3: NextAuth 认证失败
**原因**: 环境变量配置错误
**解决**: 
1. 确保 `NEXTAUTH_URL` 指向正确的域名
2. 检查 `NEXTAUTH_SECRET` 长度至少 32 字符
3. 验证 OAuth 提供商的回调 URL

### 问题 4: 数据库连接失败
**原因**: 数据库不可从 Cloudflare 网络访问
**解决**:
1. 使用支持外部连接的数据库（如 PlanetScale, Supabase）
2. 确保数据库允许来自 Cloudflare IP 的连接
3. 检查 `DATABASE_URL` 格式

## 📊 部署后验证

### 功能测试清单
- [ ] 首页加载正常
- [ ] 用户注册/登录功能
- [ ] Google OAuth 登录
- [ ] 订单创建和支付
- [ ] GitHub 邀请功能
- [ ] API 路由响应正常
- [ ] 国际化切换
- [ ] 移动端适配

### 性能检查
- [ ] 页面加载速度 < 3秒
- [ ] Core Web Vitals 达标
- [ ] CDN 缓存正常工作
- [ ] 图片优化生效

### 安全检查
- [ ] HTTPS 强制启用
- [ ] 安全头部正确设置
- [ ] CSP 策略生效
- [ ] 敏感信息不泄露

## 🎯 部署命令

```bash
# 提交所有更改
git add .
git commit -m "feat: configure Cloudflare Pages deployment with OpenNext.js"
git push origin main

# Cloudflare Pages 将自动触发部署
```

## 📞 获取帮助

如果遇到问题：
1. 检查 Cloudflare Pages 构建日志
2. 查看 [OpenNext.js 文档](https://opennext.js.org/cloudflare)
3. 参考 [Cloudflare Pages 故障排除](https://developers.cloudflare.com/pages/troubleshooting/)

---

🎉 **准备就绪！** 您的项目现在已完全配置好，可以成功部署到 Cloudflare Pages！
