import * as React from "react"
import { cn } from "@/lib/utils"
import { ChevronRight } from "lucide-react"

interface HeroSectionProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string
  subtitle?: {
    regular: string
    gradient: string
  }
  description?: string
  ctaText?: string
  ctaHref?: string
  bottomImage?: {
    light: string
    dark: string
  }
  gridOptions?: {
    angle?: number
    cellSize?: number
    opacity?: number
    lightLineColor?: string
    darkLineColor?: string
  }
}

const RetroGrid = ({
  angle = 65,
  cellSize = 60,
  opacity = 0.3,
  lightLineColor = "rgba(148, 163, 184, 0.3)",
  darkLineColor = "rgba(71, 85, 105, 0.4)",
}) => {
  const gridStyles = {
    "--grid-angle": `${angle}deg`,
    "--cell-size": `${cellSize}px`,
    "--opacity": opacity,
    "--light-line": lightLineColor,
    "--dark-line": darkLineColor,
  } as React.CSSProperties

  return (
    <div
      className={cn(
        "pointer-events-none absolute inset-0 overflow-hidden [perspective:200px]",
        `opacity-[var(--opacity)]`,
      )}
      style={gridStyles}
    >
      <div className="absolute inset-0 [transform:rotateX(var(--grid-angle))]">
        <div className="animate-grid [background-image:linear-gradient(to_right,var(--light-line)_1px,transparent_0),linear-gradient(to_bottom,var(--light-line)_1px,transparent_0)] [background-repeat:repeat] [background-size:var(--cell-size)_var(--cell-size)] [height:300vh] [inset:0%_0px] [margin-left:-200%] [transform-origin:100%_0_0] [width:600vw] dark:[background-image:linear-gradient(to_right,var(--dark-line)_1px,transparent_0),linear-gradient(to_bottom,var(--dark-line)_1px,transparent_0)]" />
      </div>
      {/* Enhanced gradient overlay for better blending */}
      <div className="absolute inset-0 bg-gradient-to-t from-white via-white/80 to-transparent to-70% dark:from-slate-950 dark:via-slate-950/80 dark:to-transparent" />
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white/90 dark:to-slate-950/90" />
    </div>
  )
}

const HeroSection = React.forwardRef<HTMLDivElement, HeroSectionProps>(
  (
    {
      className,
      title = "Build products for everyone",
      subtitle = {
        regular: "Designing your projects faster with ",
        gradient: "the largest figma UI kit.",
      },
      description = "Sed ut perspiciatis unde omnis iste natus voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae.",
      ctaText = "Browse courses",
      ctaHref = "#",
      bottomImage = {
        light: "https://farmui.vercel.app/dashboard-light.png",
        dark: "https://farmui.vercel.app/dashboard.png",
      },
      gridOptions,
      ...props
    },
    ref,
  ) => {
    return (
      <div className={cn("relative min-h-screen", className)} ref={ref} {...props}>
        {/* Enhanced Background with Better Gradients */}
        <div className="absolute inset-0 z-0 overflow-hidden">
          {/* Primary gradient background */}
          <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800" />

          {/* Enhanced radial gradient overlay */}
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_80%_80%_at_50%_-20%,rgba(120,119,198,0.12),rgba(255,255,255,0))] dark:bg-[radial-gradient(ellipse_80%_80%_at_50%_-20%,rgba(120,119,198,0.25),rgba(0,0,0,0))]" />

          {/* Secondary accent gradient */}
          <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-gradient-to-bl from-purple-100/30 via-transparent to-transparent dark:from-purple-900/20" />
          <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-gradient-to-tr from-blue-100/30 via-transparent to-transparent dark:from-blue-900/20" />
        </div>

        <section className="relative z-10 flex items-center justify-center min-h-screen">
          <RetroGrid {...gridOptions} />

          <div className="container mx-auto px-4 py-16 md:py-24 lg:py-32 max-w-7xl">
            <div className="flex flex-col items-center text-center space-y-8 lg:space-y-12">

              {/* Enhanced Title Badge */}
              <div className="group relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full blur opacity-25 group-hover:opacity-40 transition duration-300"></div>
                <h1 className="relative text-sm text-gray-700 dark:text-gray-300 font-medium mx-auto px-6 py-3 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 rounded-full w-fit shadow-sm hover:shadow-md transition-all duration-300 group-hover:scale-105">
                  {title}
                  <ChevronRight className="inline w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                </h1>
              </div>

              {/* Enhanced Main Heading with Better Typography */}
              <div className="space-y-6 max-w-5xl mx-auto">
                <h2 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-bold tracking-tight leading-none">
                  <span className="bg-clip-text text-transparent bg-gradient-to-b from-gray-900 via-gray-800 to-gray-700 dark:from-white dark:via-gray-100 dark:to-gray-300">
                    {subtitle.regular}
                  </span>
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-600 via-pink-500 to-orange-400 dark:from-purple-400 dark:via-pink-300 dark:to-orange-300 font-extrabold">
                    {subtitle.gradient}
                  </span>
                </h2>

                {/* Enhanced Description */}
                <p className="text-lg md:text-xl lg:text-2xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed font-light">
                  {description}
                </p>
              </div>

              {/* Enhanced CTA Button */}
              <div className="pt-4">
                <div className="group relative inline-block">
                  {/* Animated background glow */}
                  <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 via-pink-500 to-orange-400 rounded-full blur-sm opacity-70 group-hover:opacity-100 transition duration-300 animate-pulse"></div>

                  {/* Button container with spinning border */}
                  <div className="relative inline-block overflow-hidden rounded-full p-[2px]">
                    <div className="absolute inset-[-1000%] animate-[spin_3s_linear_infinite] bg-[conic-gradient(from_90deg_at_50%_50%,#8B5CF6_0%,#EC4899_25%,#F97316_50%,#8B5CF6_100%)]" />

                    <div className="relative inline-flex h-full w-full cursor-pointer items-center justify-center rounded-full bg-white dark:bg-gray-950 backdrop-blur-xl">
                      <a
                        href={ctaHref}
                        className="inline-flex items-center justify-center px-8 py-4 md:px-12 md:py-5 text-base md:text-lg font-semibold text-gray-900 dark:text-white bg-gradient-to-r from-white via-gray-50 to-white dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 rounded-full border border-gray-200/50 dark:border-gray-700/50 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 group-hover:bg-gradient-to-r group-hover:from-gray-50 group-hover:via-white group-hover:to-gray-50 dark:group-hover:from-gray-800 dark:group-hover:via-gray-700 dark:group-hover:to-gray-800"
                      >
                        {ctaText}
                        <ChevronRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Bottom Image Section */}
            {bottomImage && (
              <div className="mt-20 lg:mt-32 relative z-10">
                <div className="relative max-w-6xl mx-auto">
                  {/* Image glow effect */}
                  <div className="absolute -inset-4 bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-orange-500/20 rounded-2xl blur-2xl opacity-50"></div>

                  {/* Image container */}
                  <div className="relative bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm rounded-2xl p-2 border border-gray-200/50 dark:border-gray-700/50 shadow-2xl">
                    <img
                      src={bottomImage.light}
                      className="w-full rounded-xl shadow-lg border border-gray-200/50 dark:hidden"
                      alt="Dashboard preview"
                    />
                    <img
                      src={bottomImage.dark}
                      className="hidden w-full rounded-xl shadow-lg border border-gray-700/50 dark:block"
                      alt="Dashboard preview"
                    />
                  </div>

                  {/* Decorative elements */}
                  <div className="absolute -top-4 -left-4 w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full opacity-60 animate-pulse"></div>
                  <div className="absolute -bottom-4 -right-4 w-6 h-6 bg-gradient-to-br from-orange-500 to-red-500 rounded-full opacity-60 animate-pulse delay-1000"></div>
                </div>
              </div>
            )}
          </div>
        </section>
      </div>
    )
  },
)
HeroSection.displayName = "HeroSection"

export { HeroSection }
