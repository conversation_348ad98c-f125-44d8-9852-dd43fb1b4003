"use client";

import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

// Tech stack data based on the image and project requirements
const techStackData = [
  {
    name: "Next.js",
    description: "The most popular full stack React framework for production.",
    icon: "N",
    category: "Framework",
    color: "from-gray-900 to-black",
    textColor: "text-white",
    bgColor: "bg-black"
  },
  {
    name: "BetterAuth",
    description: "The best open source authentication library.",
    icon: "🔐",
    category: "Authentication",
    color: "from-blue-600 to-blue-800",
    textColor: "text-white",
    bgColor: "bg-blue-600"
  },
  {
    name: "Drizzle ORM",
    description: "Lightweight, performant, headless TypeScript ORM.",
    icon: "🌊",
    category: "Database",
    color: "from-green-600 to-green-800",
    textColor: "text-white",
    bgColor: "bg-green-600"
  },
  {
    name: "Stripe",
    description: "The best and most secure online payment service.",
    icon: "S",
    category: "Payment",
    color: "from-purple-600 to-purple-800",
    textColor: "text-white",
    bgColor: "bg-purple-600"
  },
  {
    name: "Shadcn UI",
    description: "Open source components for building modern websites.",
    icon: "//",
    category: "UI Library",
    color: "from-slate-700 to-slate-900",
    textColor: "text-white",
    bgColor: "bg-slate-700"
  },
  {
    name: "Tailwind CSS",
    description: "The CSS framework for rapid UI development.",
    icon: "🎨",
    category: "Styling",
    color: "from-cyan-500 to-blue-600",
    textColor: "text-white",
    bgColor: "bg-cyan-500"
  },
  {
    name: "MagicUI",
    description: "150+ free open source animated components and effects.",
    icon: "✨",
    category: "Animation",
    color: "from-pink-500 to-rose-600",
    textColor: "text-white",
    bgColor: "bg-pink-500"
  },
  {
    name: "Tailark",
    description: "Responsive, pre-built Shadcn/UI and Tailwindcss blocks.",
    icon: "🏗️",
    category: "Components",
    color: "from-indigo-600 to-purple-700",
    textColor: "text-white",
    bgColor: "bg-indigo-600"
  },
  {
    name: "Resend",
    description: "The best modern email service for developers.",
    icon: "R",
    category: "Email",
    color: "from-orange-500 to-red-600",
    textColor: "text-white",
    bgColor: "bg-orange-500"
  },
  {
    name: "Vercel AI SDK",
    description: "The open source AI Toolkit for TypeScript.",
    icon: "🤖",
    category: "AI",
    color: "from-gray-800 to-gray-900",
    textColor: "text-white",
    bgColor: "bg-gray-800"
  },
  {
    name: "ChatGPT",
    description: "The most powerful AI model with API access.",
    icon: "💬",
    category: "AI",
    color: "from-emerald-600 to-teal-700",
    textColor: "text-white",
    bgColor: "bg-emerald-600"
  },
  {
    name: "Fumadocs",
    description: "The best documentation framework for Next.js.",
    icon: "📚",
    category: "Documentation",
    color: "from-violet-600 to-purple-700",
    textColor: "text-white",
    bgColor: "bg-violet-600"
  }
];

export function TechStack() {
  return (
    <section className="py-24 relative overflow-hidden bg-slate-950">
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-slate-950 via-slate-900 to-slate-950" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_1000px_at_50%_200px,rgba(59,130,246,0.08),transparent)]" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_600px_at_80%_300px,rgba(139,92,246,0.06),transparent)]" />

      {/* Subtle grid pattern */}
      <div className="absolute inset-0 opacity-[0.02]" style={{
        backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0)`,
        backgroundSize: '50px 50px'
      }} />

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-blue-500/10 border border-blue-500/20 mb-6">
            <span className="text-blue-400 text-sm font-medium">TECH STACK</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
            Build with your favorite tech stack
          </h2>
          <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
            Use the latest industry-standard tech stack for your next project
          </p>
        </motion.div>

        {/* Tech Stack Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">
          {techStackData.map((tech, index) => (
            <TechCard key={tech.name} tech={tech} index={index} />
          ))}
        </div>
      </div>
    </section>
  );
}

interface TechCardProps {
  tech: {
    name: string;
    description: string;
    icon: string;
    category: string;
    color: string;
    textColor: string;
    bgColor: string;
  };
  index: number;
}

const TechCard = ({ tech, index }: TechCardProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
      className="group relative"
    >
      <div className="relative bg-slate-900/80 backdrop-blur-sm border border-slate-700/30 rounded-2xl p-6 hover:border-slate-600/50 transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/20 hover:-translate-y-2 hover:bg-slate-800/80">
        {/* Subtle glow effect */}
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* Content */}
        <div className="relative z-10">
          {/* Icon */}
          <div className={cn(
            "w-14 h-14 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg",
            tech.bgColor
          )}>
            <span className={cn("text-2xl font-bold", tech.textColor)}>
              {tech.icon}
            </span>
          </div>

          {/* Content */}
          <div className="space-y-4">
            <div>
              <h3 className="text-xl font-semibold text-white group-hover:text-blue-300 transition-colors mb-2">
                {tech.name}
              </h3>
              <span className="inline-block text-xs px-3 py-1 rounded-full bg-slate-800/80 text-slate-300 border border-slate-600/50">
                {tech.category}
              </span>
            </div>
            <p className="text-sm text-slate-300 leading-relaxed">
              {tech.description}
            </p>
          </div>
        </div>

        {/* Subtle border glow on hover */}
        <div className="absolute inset-0 rounded-2xl border border-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      </div>
    </motion.div>
  );
};
