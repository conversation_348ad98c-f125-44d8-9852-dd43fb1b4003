#!/bin/bash

# Vercel 构建脚本
# 解决 pnpm 和 npm 冲突问题

set -e

echo "🚀 开始 Vercel 构建..."

# 1. 检查 Node.js 版本
echo "📋 Node.js 版本: $(node --version)"
echo "📋 npm 版本: $(npm --version)"

# 2. 清理可能的冲突文件
echo "🧹 清理构建缓存..."
rm -rf .next
rm -rf node_modules/.cache
rm -rf .vercel

# 3. 安装依赖 (使用 npm 以兼容 Vercel)
echo "📦 安装依赖..."
npm ci --legacy-peer-deps

# 4. 生成 Prisma Client
echo "🔧 生成 Prisma Client..."
npx prisma generate

# 5. 构建 Next.js 应用
echo "🏗️ 构建 Next.js 应用..."
npm run build

echo "✅ Vercel 构建完成！"
