# Cloudflare Pages 安全头部配置

/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  X-XSS-Protection: 1; mode=block
  Permissions-Policy: camera=(), microphone=(), geolocation=()

# API 路由的 CORS 配置
/api/*
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization

# 静态资源缓存
/_next/static/*
  Cache-Control: public, max-age=31536000, immutable

# 字体文件缓存
/fonts/*
  Cache-Control: public, max-age=31536000, immutable
