{"buildCommand": "prisma generate && next build", "framework": "nextjs", "regions": ["hkg1"], "env": {"NEXTAUTH_URL": "https://${VERCEL_URL}", "VERCEL_URL": "${VERCEL_URL}", "SKIP_ENV_VALIDATION": "1"}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}]}