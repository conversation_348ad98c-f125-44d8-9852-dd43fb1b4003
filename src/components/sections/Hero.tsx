/**
 * Hero Section Component / 英雄区组件
 *
 * @description The main hero section component for landing pages, featuring animated backgrounds,
 * gradient text effects, call-to-action buttons, and responsive design. This component serves
 * as the primary focal point for user engagement and conversion.
 * @description 落地页的主要英雄区组件，具有动画背景、渐变文本效果、行动号召按钮和响应式设计。
 * 此组件作为用户参与和转化的主要焦点。
 *
 * @features
 * - Animated background with gradient effects and floating particles
 * - Responsive typography with gradient text styling
 * - Call-to-action buttons with hover animations
 * - Framer Motion animations for smooth entrance effects
 * - Full viewport height with centered content alignment
 * - Dark/light theme support with seamless transitions
 *
 * @特性
 * - 具有渐变效果和浮动粒子的动画背景
 * - 具有渐变文本样式的响应式排版
 * - 具有悬停动画的行动号召按钮
 * - Framer Motion 动画实现流畅的入场效果
 * - 全视口高度和内容居中对齐
 * - 深色/浅色主题支持和无缝过渡
 *
 * @layout
 * - Responsive design (mobile-first approach)
 * - Full viewport height minus header (min-h-[calc(100vh-4rem)])
 * - Centered content alignment with proper spacing
 * - Background animations and decorative elements
 *
 * @布局
 * - 响应式设计（移动优先方法）
 * - 全视口高度减去头部（min-h-[calc(100vh-4rem)]）
 * - 内容居中对齐和适当间距
 * - 背景动画和装饰元素
 *
 * @accessibility
 * - Semantic HTML structure with proper heading hierarchy
 * - ARIA labels for interactive elements
 * - Keyboard navigation support for all interactive elements
 * - Screen reader friendly with descriptive text
 * - High contrast support for better visibility
 *
 * @无障碍性
 * - 具有适当标题层次结构的语义化 HTML 结构
 * - 交互元素的 ARIA 标签
 * - 所有交互元素的键盘导航支持
 * - 具有描述性文本的屏幕阅读器友好
 * - 高对比度支持以提高可见性
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */

"use client";

import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import * as React from "react";

/**
 * RetroGrid component for animated background grid effect
 * RetroGrid 组件用于动画背景网格效果
 */
const RetroGrid = ({
  angle = 65,
  cellSize = 60,
  opacity = 0.3,
  lightLineColor = "rgba(148, 163, 184, 0.3)",
  darkLineColor = "rgba(71, 85, 105, 0.4)",
}) => {
  const gridStyles = {
    "--grid-angle": `${angle}deg`,
    "--cell-size": `${cellSize}px`,
    "--opacity": opacity,
    "--light-line": lightLineColor,
    "--dark-line": darkLineColor,
  } as React.CSSProperties

  return (
    <div
      className={cn(
        "pointer-events-none absolute inset-0 overflow-hidden [perspective:200px]",
        `opacity-[var(--opacity)]`,
      )}
      style={gridStyles}
    >
      <div className="absolute inset-0 [transform:rotateX(var(--grid-angle))]">
        <div className="animate-grid [background-image:linear-gradient(to_right,var(--light-line)_1px,transparent_0),linear-gradient(to_bottom,var(--light-line)_1px,transparent_0)] [background-repeat:repeat] [background-size:var(--cell-size)_var(--cell-size)] [height:300vh] [inset:0%_0px] [margin-left:-200%] [transform-origin:100%_0_0] [width:600vw] dark:[background-image:linear-gradient(to_right,var(--dark-line)_1px,transparent_0),linear-gradient(to_bottom,var(--dark-line)_1px,transparent_0)]" />
      </div>
      {/* Enhanced gradient overlay for better blending */}
      <div className="absolute inset-0 bg-gradient-to-t from-white via-white/80 to-transparent to-70% dark:from-slate-950 dark:via-slate-950/80 dark:to-transparent" />
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white/90 dark:to-slate-950/90" />
    </div>
  )
}

/**
 * Props interface for the Hero component
 * Hero 组件的属性接口
 */
interface HeroProps {
  /**
   * Hero section content data
   * 英雄区内容数据
   */
  hero: {
    /**
     * Main headline text - should be compelling and action-oriented
     * 主标题文本 - 应该引人注目且面向行动
     * @example "All-in-One SaaS Launch Solution"
     */
    title: string;

    /**
     * Supporting subtitle text - provides additional context
     * 支持性副标题文本 - 提供额外上下文
     * @example "Smart automation, instant deployment, accelerate your business growth"
     */
    subtitle: string;

    /**
     * Detailed description text - explains the value proposition
     * 详细描述文本 - 解释价值主张
     * @example "Ship SaaS Demo delivers a ready-to-use SaaS template..."
     */
    description: string;

    /**
     * Call-to-action button configuration
     * 行动号召按钮配置
     */
    cta: {
      /**
       * Primary CTA button text - main conversion action
       * 主要 CTA 按钮文本 - 主要转化行动
       * @example "Try for Free Now"
       */
      primary: string;

      /**
       * Secondary CTA button text - alternative action
       * 次要 CTA 按钮文本 - 替代行动
       * @example "See Pricing"
       */
      secondary: string;
    };

    /**
     * Optional special offer badge configuration
     * 可选的特殊优惠标签配置
     */
    badge?: {
      /**
       * Badge text content
       * 标签文本内容
       * @example "Special GIF: 20% off"
       */
      text: string;
      /**
       * Badge emoji or icon
       * 标签表情符号或图标
       * @example "🔥"
       */
      icon?: string;
    };

    /**
     * Optional social proof configuration
     * 可选的社会证明配置
     */
    socialProof?: {
      /**
       * Social proof text
       * 社会证明文本
       * @example "90+ makers ship faster with MkSaaS"
       */
      text: string;
      /**
       * Number of user avatars to show
       * 显示的用户头像数量
       * @default 6
       */
      avatarCount?: number;
    };
  };
}

/**
 * Hero - Main landing page hero section component
 * Hero - 主要落地页英雄区组件
 *
 * @description Renders the primary hero section with animated backgrounds, gradient text,
 * and call-to-action buttons. Optimized for conversion and user engagement.
 * @description 渲染具有动画背景、渐变文本和行动号召按钮的主要英雄区。
 * 针对转化和用户参与进行了优化。
 *
 * @param props - Hero component props containing content data
 * @param props.hero - Hero section content including title, subtitle, description, and CTAs
 * @returns JSX.Element - The rendered hero section
 *
 * @example Basic usage / 基本用法
 * ```tsx
 * <Hero
 *   hero={{
 *     title: "Welcome to ShipSaaS",
 *     subtitle: "Build and launch your SaaS faster",
 *     description: "Complete SaaS template with authentication, payments, and more",
 *     cta: {
 *       primary: "Get Started",
 *       secondary: "Learn More"
 *     }
 *   }}
 * />
 * ```
 *
 * @example With internationalization / 国际化使用
 * ```tsx
 * const { hero } = useTranslations('hero');
 * <Hero hero={hero} />
 * ```
 */
export function Hero({ hero }: HeroProps) {
  return (
    <section
      id="hero"
      className="relative flex items-center justify-center min-h-screen w-full overflow-hidden"
      aria-label="Hero section"
    >
      {/* Enhanced Background with Better Gradients */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        {/* Primary gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800" />

        {/* Enhanced radial gradient overlay */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_80%_80%_at_50%_-20%,rgba(120,119,198,0.12),rgba(255,255,255,0))] dark:bg-[radial-gradient(ellipse_80%_80%_at_50%_-20%,rgba(120,119,198,0.25),rgba(0,0,0,0))]" />

        {/* Secondary accent gradient */}
        <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-gradient-to-bl from-purple-100/30 via-transparent to-transparent dark:from-purple-900/20" />
        <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-gradient-to-tr from-blue-100/30 via-transparent to-transparent dark:from-blue-900/20" />
      </div>

      <div className="relative z-10 flex items-center justify-center min-h-screen">
        <RetroGrid
          angle={65}
          opacity={0.4}
          cellSize={50}
          lightLineColor="rgba(148, 163, 184, 0.25)"
          darkLineColor="rgba(71, 85, 105, 0.35)"
        />

        <div className="container mx-auto px-4 py-16 md:py-24 lg:py-32 max-w-7xl">
          <div className="flex flex-col items-center text-center space-y-8 lg:space-y-12">
            {/* Enhanced Title Badge */}
            {hero.badge && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="group relative"
              >
                <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full blur opacity-25 group-hover:opacity-40 transition duration-300"></div>
                <div className="relative text-sm text-gray-700 dark:text-gray-300 font-medium mx-auto px-6 py-3 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 rounded-full w-fit shadow-sm hover:shadow-md transition-all duration-300 group-hover:scale-105 inline-flex items-center gap-2">
                  {hero.badge.icon && <span>{hero.badge.icon}</span>}
                  <span>{hero.badge.text}</span>
                  <ChevronRight className="inline w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                </div>
              </motion.div>
            )}

            {/* Enhanced Main Heading with Better Typography */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="space-y-6 max-w-5xl mx-auto"
            >
              <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-bold tracking-tight leading-none">
                <span className="bg-clip-text text-transparent bg-gradient-to-b from-gray-900 via-gray-800 to-gray-700 dark:from-white dark:via-gray-100 dark:to-gray-300">
                  {hero.title}
                </span>
              </h1>

              {/* Enhanced Description */}
              <p className="text-lg md:text-xl lg:text-2xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed font-light">
                {hero.subtitle}
              </p>
              <p className="text-base md:text-lg text-gray-500 dark:text-gray-400 max-w-2xl mx-auto leading-relaxed">
                {hero.description}
              </p>
            </motion.div>

            {/* Enhanced CTA Button */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
              className="pt-4 flex flex-col sm:flex-row items-center justify-center gap-4"
            >
              <div className="group relative inline-block">
                {/* Animated background glow */}
                <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 via-pink-500 to-orange-400 rounded-full blur-sm opacity-70 group-hover:opacity-100 transition duration-300 animate-pulse"></div>

                {/* Button container with spinning border */}
                <div className="relative inline-block overflow-hidden rounded-full p-[2px]">
                  <div className="absolute inset-[-1000%] animate-[spin_3s_linear_infinite] bg-[conic-gradient(from_90deg_at_50%_50%,#8B5CF6_0%,#EC4899_25%,#F97316_50%,#8B5CF6_100%)]" />

                  <div className="relative inline-flex h-full w-full cursor-pointer items-center justify-center rounded-full bg-white dark:bg-gray-950 backdrop-blur-xl">
                    <Button
                      size="lg"
                      className="inline-flex items-center justify-center px-8 py-4 md:px-12 md:py-5 text-base md:text-lg font-semibold text-gray-900 dark:text-white bg-gradient-to-r from-white via-gray-50 to-white dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 rounded-full border border-gray-200/50 dark:border-gray-700/50 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 group-hover:bg-gradient-to-r group-hover:from-gray-50 group-hover:via-white group-hover:to-gray-50 dark:group-hover:from-gray-800 dark:group-hover:via-gray-700 dark:group-hover:to-gray-800"
                    >
                      {hero.cta.primary}
                      <ChevronRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                    </Button>
                  </div>
                </div>
              </div>

              <Button
                variant="outline"
                size="lg"
                className="min-w-[160px] rounded-lg px-8 h-12 text-base font-semibold border border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800 transition-all duration-300 transform hover:scale-105"
              >
                {hero.cta.secondary}
              </Button>
            </motion.div>

            {/* Enhanced User Avatars and Social Proof - Conditional Rendering */}
            {hero.socialProof && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="flex flex-col items-center gap-4 mt-8"
              >
                {/* Enhanced User Avatars */}
                <div className="flex items-center -space-x-2">
                  {Array.from({ length: hero.socialProof?.avatarCount || 6 }, (_, index) => {
                    const colors = [
                      'from-blue-400 to-blue-600',
                      'from-green-400 to-green-600',
                      'from-purple-400 to-purple-600',
                      'from-orange-400 to-orange-600',
                      'from-pink-400 to-pink-600',
                      'from-indigo-400 to-indigo-600',
                      'from-cyan-400 to-cyan-600',
                      'from-red-400 to-red-600'
                    ];
                    const avatarCount = hero.socialProof?.avatarCount || 6;
                    const isLast = index === avatarCount - 1;
                    const letter = isLast ? '+' : String.fromCharCode(65 + index); // A, B, C, ... or +

                    return (
                      <div
                        key={index}
                        className={`w-12 h-12 rounded-full bg-gradient-to-r ${colors[index % colors.length]} border-2 border-white dark:border-gray-800 flex items-center justify-center text-white text-sm font-semibold shadow-lg hover:scale-110 transition-transform duration-300`}
                      >
                        {letter}
                      </div>
                    );
                  })}
                </div>

                {/* Enhanced Social Proof Text */}
                <p className="text-sm md:text-base text-gray-600 dark:text-gray-400 font-medium">
                  {hero.socialProof.text}
                </p>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
