#!/bin/bash

# Cloudflare Pages 构建脚本
# 确保使用 OpenNext.js Cloudflare 而不是 @cloudflare/next-on-pages

set -e

echo "🚀 开始 Cloudflare Pages 构建..."

# 1. 生成 Prisma Client
echo "📦 生成 Prisma Client..."
npx prisma generate

# 2. 构建 Next.js 应用
echo "🏗️ 构建 Next.js 应用..."
npx next build

# 3. 使用 OpenNext.js Cloudflare 适配器
echo "⚡ 使用 OpenNext.js Cloudflare 适配器..."
npx opennextjs-cloudflare build

echo "✅ Cloudflare Pages 构建完成！"
echo "📁 输出目录: .open-next/"
echo "🌐 Worker 文件: .open-next/worker.js"
